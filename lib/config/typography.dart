import 'package:flutter/material.dart';
import 'design_system.dart';
import 'app_typography.dart';

/// Centralized Typography System for HSK Screens
///
/// This class provides a consistent typography system across all HSK learning screens,
/// using the comprehensive AppTypography system with NotoSansSC for optimal Chinese
/// character rendering and modern design.
///
/// Benefits:
/// - ✨ Consistency: Unified with app-wide typography system
/// - 🔧 Maintainability: Centralized typography management
/// - 📱 Performance: Optimized Chinese character rendering
/// - 🌐 Accessibility: Better Unicode support with responsive scaling
/// - 🎨 Professional: Modern, clean typography with proper hierarchy
///
/// @deprecated Use AppTypography.getHSKStyles(context) for new implementations
class HskTypography {
  // Private constructor to prevent instantiation
  HskTypography._();

  /// Base font family for all HSK screens
  /// @deprecated Use AppTypography.sansSerifFont instead
  static const String _fontFamily = AppTypography.sansSerifFont;

  /// Font weight for all text (Bold for better readability)
  /// @deprecated Use AppTypography.bold instead
  static const FontWeight _fontWeight = AppTypography.bold;

  // =====================================================
  // APP TITLE & HEADERS
  // =====================================================

  /// Main app title (e.g., "CHINESE IN FLOW")
  static const TextStyle appTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 28.0,
    letterSpacing: 1.2,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(1, 1),
        blurRadius: DesignSystem.elevationS + 1.0,
        color: Color.fromARGB(150, 0, 0, 0),
      ),
    ],
  );

  /// Screen titles in app bars
  static const TextStyle screenTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 20.0,
    color: Colors.white,
  );

  /// Stage/progress indicators
  static const TextStyle stageIndicator = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 16.0,
    color: Colors.white,
  );

  // =====================================================
  // CHINESE CHARACTER DISPLAY
  // =====================================================

  /// Large Chinese characters (main display)
  static const TextStyle chineseCharacterLarge = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 64.0,
    color: Colors.white,
  );

  /// Medium Chinese characters (buttons, smaller displays)
  static const TextStyle chineseCharacterMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 30.0,
    color: Colors.white,
  );

  /// Small Chinese characters (compact displays)
  static const TextStyle chineseCharacterSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 24.0,
    color: Colors.white,
  );

  // =====================================================
  // PINYIN & ENGLISH TEXT
  // =====================================================

  /// Pinyin text (medium size) - use with theme colors
  static const TextStyle pinyinMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 26.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Pinyin text (small size) - use with theme colors
  static const TextStyle pinyinSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: FontWeight.normal, // Normal weight for pinyin readability
    fontSize: 16.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// English translation (medium) - use with theme colors
  static const TextStyle englishMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 20.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
  );

  /// English translation (small) - use with theme colors
  static const TextStyle englishSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 13.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
  );

  // =====================================================
  // BUTTONS & INTERACTIVE ELEMENTS
  // =====================================================

  /// Button text (primary actions) - use with theme colors
  static const TextStyle buttonPrimary = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 18.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Button text (secondary actions) - use with theme colors
  static const TextStyle buttonSecondary = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 16.0,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Small button labels
  static const TextStyle buttonLabel = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: FontWeight.normal,
    fontSize: 12.0,
    color: Colors.grey,
  );

  // =====================================================
  // FEEDBACK & STATUS MESSAGES
  // =====================================================

  /// Success messages (e.g., "Correct!", "Well Done!")
  static const TextStyle feedbackSuccess = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 26.0,
    color: Colors.green,
  );

  /// Error messages (e.g., "Time over", "Sudden Death!")
  static const TextStyle feedbackError = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 26.0,
    color: Colors.red,
  );

  /// Congratulations messages
  static const TextStyle congratulations = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 22.0,
    color: Colors.white,
  );

  /// End session messages
  static const TextStyle endSession = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 48.0,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(2.0, 2.0),
        blurRadius: DesignSystem.elevationS + 1.0,
        color: Color.fromARGB(128, 0, 0, 0),
      ),
    ],
  );

  // =====================================================
  // COMPLETION SCREENS
  // =====================================================

  /// Completion screen title
  static const TextStyle completionTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 36.0,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(1.0, 1.0),
        blurRadius: DesignSystem.elevationS,
        color: Color.fromARGB(100, 0, 0, 0),
      ),
    ],
  );

  /// Completion screen statistics
  static const TextStyle completionStats = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 28.0,
    color: Colors.white,
  );

  /// Completion screen description
  static const TextStyle completionDescription = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: 18.0,
    color: Colors.white,
  );

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates a responsive text style based on screen size
  static TextStyle responsive(BuildContext context, TextStyle baseStyle) {
    final textScaler = MediaQuery.textScalerOf(context);
    final scaledFontSize = baseStyle.fontSize != null
        ? (baseStyle.fontSize! *
            textScaler.scale(baseStyle.fontSize!) /
            baseStyle.fontSize!)
        : null;

    return baseStyle.copyWith(
      fontSize: scaledFontSize?.clamp(
        (baseStyle.fontSize ?? 14.0) * 0.8,
        (baseStyle.fontSize ?? 14.0) * 1.3,
      ),
    );
  }

  /// Creates a text style with custom color while maintaining font family
  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  /// Creates a text style with custom size while maintaining font family
  static TextStyle withSize(TextStyle baseStyle, double fontSize) {
    return baseStyle.copyWith(fontSize: fontSize);
  }
}
