import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'design_system.dart';

/// Comprehensive platform adaptation system for Dasso Reader
///
/// Provides platform-specific design constants, behaviors, and utilities
/// to ensure the app feels native on both iOS and Android while maintaining
/// the core Material 3 design system.
class PlatformAdaptations {
  // =====================================================
  // PLATFORM DETECTION
  // =====================================================

  /// Returns true if running on iOS
  static bool get isIOS => defaultTargetPlatform == TargetPlatform.iOS;

  /// Returns true if running on Android
  static bool get isAndroid => defaultTargetPlatform == TargetPlatform.android;

  /// Returns true if running on a mobile platform (iOS or Android)
  static bool get isMobile => isIOS || isAndroid;

  /// Returns true if running on desktop platforms
  static bool get isDesktop =>
      defaultTargetPlatform == TargetPlatform.windows ||
      defaultTargetPlatform == TargetPlatform.macOS ||
      defaultTargetPlatform == TargetPlatform.linux;

  /// Returns the current platform as a string for debugging
  static String get platformName => defaultTargetPlatform.name;

  // =====================================================
  // PLATFORM-SPECIFIC DESIGN CONSTANTS
  // =====================================================

  /// Platform-appropriate border radius values
  static double get adaptiveBorderRadius {
    if (isIOS) return 12.0; // iOS prefers slightly larger radius
    return DesignSystem.radiusM; // Android Material 3 standard
  }

  /// Platform-appropriate button height
  static double get adaptiveButtonHeight {
    if (isIOS) return 50.0; // iOS standard button height
    return 48.0; // Material 3 standard
  }

  /// Platform-appropriate list item height
  static double get adaptiveListItemHeight {
    if (isIOS) return 56.0; // iOS standard
    return 56.0; // Material 3 standard (same for consistency)
  }

  /// Platform-appropriate app bar height
  static double get adaptiveAppBarHeight {
    if (isIOS) return 44.0; // iOS navigation bar height
    return kToolbarHeight; // Material standard (56.0)
  }

  /// Platform-appropriate elevation values
  static double get adaptiveElevation {
    if (isIOS) return DesignSystem.elevationNone; // iOS prefers flat design
    return DesignSystem.elevationS; // Material elevation
  }

  /// Platform-appropriate shadow blur radius
  static double get adaptiveShadowBlur {
    if (isIOS) return DesignSystem.elevationL; // iOS shadow style
    return DesignSystem.elevationM; // Material shadow style
  }

  // =====================================================
  // PLATFORM-SPECIFIC BEHAVIORS
  // =====================================================

  /// Returns platform-appropriate scroll physics
  static ScrollPhysics get adaptiveScrollPhysics {
    if (isIOS) return const BouncingScrollPhysics();
    return const ClampingScrollPhysics(); // Android default
  }

  /// Returns platform-appropriate page transition duration
  static Duration get adaptiveTransitionDuration {
    if (isIOS) return const Duration(milliseconds: 350); // iOS standard
    return const Duration(milliseconds: 300); // Material standard
  }

  /// Returns platform-appropriate haptic feedback intensity
  static bool get shouldUseHapticFeedback {
    return isIOS; // iOS users expect haptic feedback more than Android
  }

  // =====================================================
  // ADAPTIVE COMPONENT FACTORIES
  // =====================================================

  /// Creates platform-appropriate page route
  static PageRoute<T> createPageRoute<T>({
    required Widget page,
    RouteSettings? settings,
    bool fullscreenDialog = false,
  }) {
    if (isIOS) {
      return CupertinoPageRoute<T>(
        builder: (context) => page,
        settings: settings,
        fullscreenDialog: fullscreenDialog,
      );
    }
    return MaterialPageRoute<T>(
      builder: (context) => page,
      settings: settings,
      fullscreenDialog: fullscreenDialog,
    );
  }

  /// Creates platform-appropriate modal route
  static PageRoute<T> createModalRoute<T>({
    required Widget page,
    RouteSettings? settings,
  }) {
    return createPageRoute<T>(
      page: page,
      settings: settings,
      fullscreenDialog: true,
    );
  }

  // =====================================================
  // PLATFORM-SPECIFIC STYLING
  // =====================================================

  /// Returns platform-appropriate button style
  static ButtonStyle getAdaptiveButtonStyle(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isIOS) {
      return ElevatedButton.styleFrom(
        elevation: DesignSystem.elevationNone,
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(adaptiveBorderRadius),
        ),
        minimumSize: Size.fromHeight(adaptiveButtonHeight),
      );
    }

    // Material 3 style for Android
    return ElevatedButton.styleFrom(
      elevation: adaptiveElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(adaptiveBorderRadius),
      ),
      minimumSize: Size.fromHeight(adaptiveButtonHeight),
    );
  }

  /// Returns platform-appropriate card decoration
  static BoxDecoration getAdaptiveCardDecoration(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isIOS) {
      return BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(adaptiveBorderRadius),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: adaptiveShadowBlur,
            offset: const Offset(0, DesignSystem.elevationS),
          ),
        ],
      );
    }

    // Material 3 style for Android
    return BoxDecoration(
      color: colorScheme.surfaceContainerLow,
      borderRadius: BorderRadius.circular(adaptiveBorderRadius),
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withValues(alpha: 0.15),
          blurRadius: adaptiveShadowBlur,
          offset: const Offset(0, DesignSystem.elevationXS),
        ),
      ],
    );
  }

  // =====================================================
  // PLATFORM-SPECIFIC COLORS
  // =====================================================

  /// Returns platform-appropriate divider color
  static Color getAdaptiveDividerColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isIOS) {
      return colorScheme.outline.withValues(alpha: 0.3);
    }
    return colorScheme.outline.withValues(alpha: 0.2);
  }

  /// Returns platform-appropriate overlay color for pressed states
  static Color getAdaptiveOverlayColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isIOS) {
      return colorScheme.onSurface.withValues(alpha: 0.1);
    }
    return colorScheme.onSurface.withValues(alpha: 0.08);
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Returns platform-appropriate loading indicator
  static Widget getAdaptiveLoadingIndicator({
    Color? color,
    double? strokeWidth,
  }) {
    if (isIOS) {
      return CupertinoActivityIndicator(
        color: color,
      );
    }
    return CircularProgressIndicator(
      color: color,
      strokeWidth: strokeWidth ?? 4.0,
    );
  }

  /// Returns platform-appropriate switch widget
  static Widget getAdaptiveSwitch({
    required bool value,
    required ValueChanged<bool> onChanged,
    Color? activeColor,
  }) {
    if (isIOS) {
      return CupertinoSwitch(
        value: value,
        onChanged: onChanged,
        activeTrackColor: activeColor,
      );
    }
    return Switch(
      value: value,
      onChanged: onChanged,
      activeTrackColor: activeColor,
    );
  }

  /// Returns platform-appropriate slider widget
  static Widget getAdaptiveSlider({
    required double value,
    required ValueChanged<double> onChanged,
    double min = 0.0,
    double max = 1.0,
    int? divisions,
    Color? activeColor,
  }) {
    if (isIOS) {
      return CupertinoSlider(
        value: value,
        onChanged: onChanged,
        min: min,
        max: max,
        divisions: divisions,
        activeColor: activeColor,
      );
    }
    return Slider(
      value: value,
      onChanged: onChanged,
      min: min,
      max: max,
      divisions: divisions,
      activeColor: activeColor,
    );
  }
}
